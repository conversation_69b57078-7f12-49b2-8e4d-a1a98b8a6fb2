import { getApp } from '@react-native-firebase/app';
import { getAuth } from '@react-native-firebase/auth';
import { getFirestore, collection } from '@react-native-firebase/firestore';
import { getAnalytics } from '@react-native-firebase/analytics';
import { getCrashlytics } from '@react-native-firebase/crashlytics';
import getPerf from '@react-native-firebase/perf';

// Firebase is automatically initialized with react-native-firebase
// using the google-services.json and GoogleService-Info.plist files

// Get Firebase app instance - use the default app
export const app = getApp();

// Export auth instance using modular API
export const auth = getAuth(app);

// Export firestore instance using modular API
export const db = getFirestore(app);

// Export analytics instance using modular API
export const analytics = getAnalytics();

// Export crashlytics instance using modular API
export const crashlytics = getCrashlytics();

// Export performance instance using modular API
export const perf = getPerf();

// Export collection references using modular API
export const dietPreferenceCollection = collection(db, 'dietPreferences');
export const conversationCollection = collection(db, 'conversations');
export const inventoryCollection = collection(db, 'inventory');
export const groceryListCollection = collection(db, 'groceryList');
export const generatedRecipesCollection = collection(db, 'generatedRecipes');
export const usersCollection = collection(db, 'users');
export const savedRecipesCollection = collection(db, 'savedRecipes');

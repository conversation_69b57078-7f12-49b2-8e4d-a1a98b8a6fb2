import { DarkTheme, De<PERSON>ultTheme, ThemeProvider } from '@react-navigation/native';
import { PaperProvider, MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { useColorScheme } from 'react-native';
import 'react-native-reanimated';
import { GroceryListProvider } from '@/contexts/GroceryListContext';
import { AuthProvider } from '@/contexts/AuthContext';
import { InventoryProvider } from '@/contexts/InventoryContext';
import analyticsService from '@/services/AnalyticsService';
import crashlyticsService from '@/services/CrashlyticsService';
import performanceService from '@/services/PerformanceService';
import logger from '@/services/logger';
import { USER_PROPERTIES } from '@/constants/Analytics';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme() || 'light';
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    const initializeServices = async () => {
      try {
        // Initialize Firebase services
        await Promise.all([
          analyticsService.initialize(),
          crashlyticsService.initialize(),
          performanceService.initialize(),
        ]);

        // Track app startup
        const startupTraceId = await performanceService.trackAppStartup();

        // Set initial user properties
        await analyticsService.setUserProperties({
          [USER_PROPERTIES.APP_VERSION]: '1.0.0',
          [USER_PROPERTIES.ONBOARDING_COMPLETED]: 'false',
        });

        // Complete app startup trace
        await performanceService.stopTrace(startupTraceId, true);

        logger.info('Firebase services initialized successfully');
      } catch (error) {
        logger.error('Failed to initialize Firebase services', error instanceof Error ? error : undefined, {
          action: 'app_initialization',
          screen: 'RootLayout',
        });
      }
    };

    if (loaded) {
      SplashScreen.hideAsync();
      initializeServices();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  // Define the themes
  const paperTheme = colorScheme === 'dark' ? MD3DarkTheme : MD3LightTheme;
  const navigationTheme = colorScheme === 'dark' ? DarkTheme : DefaultTheme;

  return (
    <PaperProvider theme={paperTheme}>
      <ThemeProvider value={navigationTheme}>
        <AuthProvider>
          <InventoryProvider>
            <GroceryListProvider>
              <Stack>
                <Stack.Screen name='index' options={{ headerShown: false }} />
                <Stack.Screen name='(tabs)' options={{ headerShown: false, title: 'Home' }} />
                <Stack.Screen name='onboarding' options={{ headerShown: false }} />
                <Stack.Screen
                  name='camera'
                  options={{
                    title: '',
                    headerBackTitle: 'Back',
                  }}
                />
                <Stack.Screen
                  name='grocery-list'
                  options={{
                    title: 'Grocery List',
                    headerShown: false,
                  }}
                />
              </Stack>
              <StatusBar style='auto' />
            </GroceryListProvider>
          </InventoryProvider>
        </AuthProvider>
      </ThemeProvider>
    </PaperProvider>
  );
}

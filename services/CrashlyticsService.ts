/**
 * Firebase Crashlytics Service
 * Centralized service for crash reporting, error tracking, and production debugging
 */

import crashlytics from '@react-native-firebase/crashlytics';
import { ERROR_CATEGORIES } from '@/constants/Analytics';

export interface ErrorContext {
  userId?: string;
  screen?: string;
  action?: string;
  apiEndpoint?: string;
  httpStatus?: number;
  additionalData?: Record<string, any>;
}

export interface CustomError {
  name: string;
  message: string;
  category: string;
  context?: ErrorContext;
  stack?: string;
}

class CrashlyticsService {
  private isInitialized = false;
  private userId: string | null = null;

  /**
   * Initialize Crashlytics service
   */
  async initialize(): Promise<void> {
    try {
      // Enable crashlytics collection
      await crashlytics().setCrashlyticsCollectionEnabled(true);
      this.isInitialized = true;
      // Note: Using console.log directly here to avoid circular dependency
      console.log('Crashlytics service initialized successfully');
    } catch (error) {
      // Note: Using console.error directly here to avoid circular dependency
      console.error('Failed to initialize Crashlytics service:', error);
    }
  }

  /**
   * Set user identifier for crash reports
   */
  async setUserId(userId: string): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      await crashlytics().setUserId(userId);
      this.userId = userId;
      console.log('Crashlytics user ID set:', userId);
    } catch (error) {
      console.error('Failed to set Crashlytics user ID:', error);
    }
  }

  /**
   * Set custom attributes for crash reports
   */
  async setAttributes(attributes: Record<string, string>): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      for (const [key, value] of Object.entries(attributes)) {
        await crashlytics().setAttribute(key, value);
      }
      
      // Note: Using console.log directly here to avoid circular dependency
      console.log('Crashlytics attributes set:', attributes);
    } catch (error) {
      // Note: Using console.error directly here to avoid circular dependency
      console.error('Failed to set Crashlytics attributes:', error);
    }
  }

  /**
   * Log a custom error with context
   */
  async recordError(error: Error | CustomError, context?: ErrorContext): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Set context attributes if provided
      if (context) {
        await this.setContextAttributes(context);
      }

      // Log the error
      if (error instanceof Error) {
        crashlytics().recordError(error);
      } else {
        // Create a proper Error object for custom errors
        const errorObj = new Error(error.message);
        errorObj.name = error.name;
        errorObj.stack = error.stack;
        crashlytics().recordError(errorObj);
      }

      // Note: Using console.log directly here to avoid circular dependency
      console.log('Error recorded to Crashlytics:', error.message);
    } catch (crashError) {
      // Note: Using console.error directly here to avoid circular dependency
      console.error('Failed to record error to Crashlytics:', crashError);
      // Fallback to console logging
      console.error('Original error:', error);
    }
  }

  /**
   * Log a non-fatal error with category and context
   */
  async logError(
    message: string, 
    category: string = ERROR_CATEGORIES.API, 
    context?: ErrorContext
  ): Promise<void> {
    const customError: CustomError = {
      name: `${category.toUpperCase()}_ERROR`,
      message,
      category,
      context,
      stack: new Error().stack,
    };

    await this.recordError(customError, context);
  }

  /**
   * Log API errors with detailed context
   */
  async logApiError(
    endpoint: string, 
    httpStatus: number, 
    errorMessage: string, 
    context?: Partial<ErrorContext>
  ): Promise<void> {
    const apiContext: ErrorContext = {
      ...context,
      apiEndpoint: endpoint,
      httpStatus,
      action: 'api_call',
    };

    await this.logError(
      `API Error: ${errorMessage} (${httpStatus}) at ${endpoint}`,
      ERROR_CATEGORIES.API,
      apiContext
    );
  }

  /**
   * Log network errors
   */
  async logNetworkError(
    endpoint: string, 
    errorMessage: string, 
    context?: Partial<ErrorContext>
  ): Promise<void> {
    const networkContext: ErrorContext = {
      ...context,
      apiEndpoint: endpoint,
      action: 'network_request',
    };

    await this.logError(
      `Network Error: ${errorMessage} at ${endpoint}`,
      ERROR_CATEGORIES.NETWORK,
      networkContext
    );
  }

  /**
   * Log authentication errors
   */
  async logAuthError(errorMessage: string, context?: Partial<ErrorContext>): Promise<void> {
    await this.logError(
      `Auth Error: ${errorMessage}`,
      ERROR_CATEGORIES.AUTHENTICATION,
      context
    );
  }

  /**
   * Log data processing errors
   */
  async logDataProcessingError(
    operation: string, 
    errorMessage: string, 
    context?: Partial<ErrorContext>
  ): Promise<void> {
    const dataContext: ErrorContext = {
      ...context,
      action: operation,
    };

    await this.logError(
      `Data Processing Error: ${errorMessage} during ${operation}`,
      ERROR_CATEGORIES.DATA_PROCESSING,
      dataContext
    );
  }

  /**
   * Log UI errors
   */
  async logUIError(
    screen: string, 
    errorMessage: string, 
    context?: Partial<ErrorContext>
  ): Promise<void> {
    const uiContext: ErrorContext = {
      ...context,
      screen,
      action: 'ui_interaction',
    };

    await this.logError(
      `UI Error: ${errorMessage} on ${screen}`,
      ERROR_CATEGORIES.UI,
      uiContext
    );
  }

  /**
   * Log a custom message for debugging
   */
  async log(message: string): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      crashlytics().log(message);
      console.log('Crashlytics log:', message);
    } catch (error) {
      console.error('Failed to log to Crashlytics:', error);
    }
  }

  /**
   * Force a crash (for testing purposes only)
   */
  async crash(): Promise<void> {
    if (__DEV__) {
      console.warn('Crash method called in development mode - ignoring');
      return;
    }
    
    crashlytics().crash();
  }

  /**
   * Check if a crash occurred on the previous app session
   */
  async checkForUnsentReports(): Promise<boolean> {
    try {
      return await crashlytics().checkForUnsentReports();
    } catch (error) {
      console.error('Failed to check for unsent reports:', error);
      return false;
    }
  }

  /**
   * Send any unsent crash reports
   */
  async sendUnsentReports(): Promise<void> {
    try {
      crashlytics().sendUnsentReports();
      console.log('Unsent crash reports sent');
    } catch (error) {
      console.error('Failed to send unsent reports:', error);
    }
  }

  /**
   * Set context attributes for crash reports
   */
  private async setContextAttributes(context: ErrorContext): Promise<void> {
    const attributes: Record<string, string> = {};

    if (context.userId) attributes.userId = context.userId;
    if (context.screen) attributes.screen = context.screen;
    if (context.action) attributes.action = context.action;
    if (context.apiEndpoint) attributes.apiEndpoint = context.apiEndpoint;
    if (context.httpStatus) attributes.httpStatus = context.httpStatus.toString();

    // Add additional data as JSON string if present
    if (context.additionalData) {
      try {
        attributes.additionalData = JSON.stringify(context.additionalData);
      } catch (error) {
        attributes.additionalData = 'Failed to serialize additional data';
      }
    }

    await this.setAttributes(attributes);
  }

  /**
   * Get current user ID
   */
  getCurrentUserId(): string | null {
    return this.userId;
  }

  /**
   * Check if Crashlytics is initialized
   */
  isCrashlyticsInitialized(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const crashlyticsService = new CrashlyticsService();
export default crashlyticsService;

/**
 * Firebase Analytics Service
 * Centralized service for tracking user events, screen views, and user properties
 */

import {
  getAnalytics,
  logEvent,
  logScreenView,
  setAnalyticsCollectionEnabled,
  setUserId,
  setUserProperty,
} from '@react-native-firebase/analytics';
import logger from '@/services/logger';
import {
  ANALYTICS_EVENTS,
  ANALYTICS_PARAMS,
  PARAM_CONSTRAINTS,
  AnalyticsEventName,
  AnalyticsEventParams,
  UserProperties,
  ScreenName,
} from '@/constants/Analytics';

class AnalyticsService {
  private isInitialized = false;
  private userId: string | null = null;
  private analytics = getAnalytics();

  /**
   * Initialize analytics service
   */
  async initialize(): Promise<void> {
    try {
      // Enable analytics collection
      await setAnalyticsCollectionEnabled(this.analytics, true);
      this.isInitialized = true;
      logger.info('Analytics service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Analytics service', error instanceof Error ? error : undefined);
    }
  }

  /**
   * Set user ID for analytics tracking
   */
  async setUserId(userId: string): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      await setUserId(this.analytics, userId);
      this.userId = userId;
      logger.info(`Analytics user ID set: ${userId}`);
    } catch (error) {
      logger.error('Failed to set analytics user ID', error instanceof Error ? error : undefined);
    }
  }

  /**
   * Set user properties for analytics
   */
  async setUserProperties(properties: UserProperties): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Validate and sanitize properties
      const sanitizedProperties = this.sanitizeUserProperties(properties);

      for (const [key, value] of Object.entries(sanitizedProperties)) {
        if (value !== undefined) {
          await setUserProperty(this.analytics, key, String(value));
        }
      }

      logger.info('User properties set', { additionalData: sanitizedProperties });
    } catch (error) {
      logger.error('Failed to set user properties', error instanceof Error ? error : undefined);
    }
  }

  /**
   * Track a custom event
   */
  async trackEvent(eventName: AnalyticsEventName, parameters?: AnalyticsEventParams): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const sanitizedParams = parameters ? this.sanitizeEventParams(parameters) : {};

      await logEvent(this.analytics, eventName as any, sanitizedParams);
      logger.info(`Analytics event tracked: ${eventName}`, { additionalData: sanitizedParams });
    } catch (error) {
      logger.error(`Failed to track event ${eventName}`, error instanceof Error ? error : undefined);
    }
  }

  /**
   * Track screen view
   */
  async trackScreenView(screenName: ScreenName, screenClass?: string): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      await logScreenView(this.analytics, {
        screen_name: screenName,
        screen_class: screenClass || screenName,
      });

      logger.info(`Screen view tracked: ${screenName}`);
    } catch (error) {
      logger.error(`Failed to track screen view ${screenName}`, error instanceof Error ? error : undefined);
    }
  }

  /**
   * Track user authentication
   */
  async trackUserSignIn(method: string = 'anonymous'): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.USER_SIGNED_IN, {
      [ANALYTICS_PARAMS.USER_JOURNEY_STEP]: 'authentication',
      method,
    });
  }

  /**
   * Track onboarding events
   */
  async trackOnboardingStarted(): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.ONBOARDING_STARTED, {
      [ANALYTICS_PARAMS.USER_JOURNEY_STEP]: 'onboarding_start',
    });
  }

  async trackOnboardingCompleted(dietPreferences: any): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.ONBOARDING_COMPLETED, {
      [ANALYTICS_PARAMS.USER_JOURNEY_STEP]: 'onboarding_complete',
      [ANALYTICS_PARAMS.DIET_TYPE]: dietPreferences.diet,
      [ANALYTICS_PARAMS.COOKING_EXPERIENCE]: dietPreferences.experience,
      [ANALYTICS_PARAMS.CALORIES]: dietPreferences.calories,
    });
  }

  /**
   * Track recipe interactions
   */
  async trackRecipeGenerated(recipeCount: number, mealType: string, generationTimeMs: number): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.RECIPE_GENERATED, {
      [ANALYTICS_PARAMS.RECIPE_COUNT]: recipeCount,
      [ANALYTICS_PARAMS.MEAL_TYPE]: mealType,
      [ANALYTICS_PARAMS.GENERATION_TIME_MS]: generationTimeMs,
    });
  }

  async trackRecipeDetailsViewed(recipeId: string, recipeTitle: string, mealType: string): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.RECIPE_DETAILS_VIEWED, {
      [ANALYTICS_PARAMS.RECIPE_ID]: recipeId,
      [ANALYTICS_PARAMS.RECIPE_TITLE]: recipeTitle,
      [ANALYTICS_PARAMS.MEAL_TYPE]: mealType,
    });
  }

  async trackRecipeFavorited(recipeId: string, isFavorited: boolean): Promise<void> {
    const eventName = isFavorited ? ANALYTICS_EVENTS.RECIPE_FAVORITED : ANALYTICS_EVENTS.RECIPE_UNFAVORITED;
    await this.trackEvent(eventName, {
      [ANALYTICS_PARAMS.RECIPE_ID]: recipeId,
    });
  }

  /**
   * Track inventory actions
   */
  async trackPhotosAnalyzed(photosCount: number, ingredientsFound: number, analysisTimeMs: number): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.PHOTOS_ANALYZED, {
      [ANALYTICS_PARAMS.PHOTOS_COUNT]: photosCount,
      [ANALYTICS_PARAMS.INGREDIENTS_FOUND]: ingredientsFound,
      [ANALYTICS_PARAMS.ANALYSIS_TIME_MS]: analysisTimeMs,
    });
  }

  async trackInventoryItemAdded(itemName: string, categoryName: string): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.INVENTORY_ITEM_ADDED, {
      [ANALYTICS_PARAMS.ITEM_NAME]: itemName,
      [ANALYTICS_PARAMS.CATEGORY_NAME]: categoryName,
    });
  }

  /**
   * Track grocery list actions
   */
  async trackGroceryItemAdded(itemName: string, quantity: number): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.GROCERY_ITEM_ADDED, {
      [ANALYTICS_PARAMS.ITEM_NAME]: itemName,
      [ANALYTICS_PARAMS.QUANTITY]: quantity,
    });
  }

  /**
   * Track chat interactions
   */
  async trackChatMessageSent(messageLength: number): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.CHAT_MESSAGE_SENT, {
      [ANALYTICS_PARAMS.MESSAGE_LENGTH]: messageLength,
    });
  }

  async trackAIResponseReceived(responseTimeMs: number, conversationLength: number): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.AI_RESPONSE_RECEIVED, {
      [ANALYTICS_PARAMS.RESPONSE_TIME_MS]: responseTimeMs,
      [ANALYTICS_PARAMS.CONVERSATION_LENGTH]: conversationLength,
    });
  }

  /**
   * Track user feedback submission
   */
  async trackUserFeedbackSubmitted(category: string): Promise<void> {
    await this.trackEvent(ANALYTICS_EVENTS.USER_FEEDBACK_SUBMITTED, {
      [ANALYTICS_PARAMS.FEEDBACK_TYPE]: category,
    });
  }

  /**
   * Sanitize event parameters to meet Firebase constraints
   */
  private sanitizeEventParams(params: AnalyticsEventParams): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(params)) {
      if (value === undefined || value === null) continue;

      let sanitizedKey = key.substring(0, 40); // Firebase key limit
      let sanitizedValue = value;

      if (typeof value === 'string') {
        sanitizedValue = value.substring(0, PARAM_CONSTRAINTS.MAX_STRING_LENGTH);
      } else if (Array.isArray(value)) {
        sanitizedValue = value.slice(0, PARAM_CONSTRAINTS.MAX_ARRAY_LENGTH);
      }

      sanitized[sanitizedKey] = sanitizedValue;
    }

    return sanitized;
  }

  /**
   * Sanitize user properties
   */
  private sanitizeUserProperties(properties: UserProperties): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(properties)) {
      if (value === undefined || value === null) continue;

      let sanitizedKey = key.substring(0, 24); // Firebase user property key limit
      let sanitizedValue = value;

      if (typeof value === 'string') {
        sanitizedValue = value.substring(0, 36); // Firebase user property value limit
      }

      sanitized[sanitizedKey] = sanitizedValue;
    }

    return sanitized;
  }

  /**
   * Get current user ID
   */
  getCurrentUserId(): string | null {
    return this.userId;
  }

  /**
   * Check if analytics is initialized
   */
  isAnalyticsInitialized(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();
export default analyticsService;

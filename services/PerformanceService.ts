/**
 * Firebase Performance Monitoring Service
 * Centralized service for tracking app performance, custom traces, and metrics
 */

import { perf } from '@/firebase/firebaseConfig';
import { PERFORMANCE_TRACES, ANALYTICS_PARAMS } from '@/constants/Analytics';
import analyticsService from './AnalyticsService';

export interface PerformanceMetrics {
  duration: number;
  success: boolean;
  errorMessage?: string;
  customAttributes?: Record<string, string>;
}

class PerformanceService {
  private isInitialized = false;
  private activeTraces = new Map<string, any>();

  /**
   * Initialize Performance Monitoring service
   */
  async initialize(): Promise<void> {
    try {
      // Performance monitoring is automatically enabled with Firebase
      this.isInitialized = true;
      console.log('Performance monitoring service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Performance monitoring service:', error);
    }
  }

  /**
   * Start a custom performance trace
   */
  async startTrace(traceName: string): Promise<string> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const trace = await perf.startTrace(traceName);
      const traceId = `${traceName}_${Date.now()}`;
      this.activeTraces.set(traceId, {
        trace,
        startTime: Date.now(),
        traceName,
      });

      console.log(`Performance trace started: ${traceName}`);
      return traceId;
    } catch (error) {
      console.error(`Failed to start trace ${traceName}:`, error);
      return '';
    }
  }

  /**
   * Stop a custom performance trace
   */
  async stopTrace(
    traceId: string,
    success: boolean = true,
    attributes?: Record<string, string>
  ): Promise<PerformanceMetrics | null> {
    try {
      const traceData = this.activeTraces.get(traceId);
      if (!traceData) {
        console.warn(`No active trace found for ID: ${traceId}`);
        return null;
      }

      const { trace, startTime, traceName } = traceData;
      const duration = Date.now() - startTime;

      // Set custom attributes
      if (attributes) {
        for (const [key, value] of Object.entries(attributes)) {
          await trace.putAttribute(key, value);
        }
      }

      // Set success metric
      await trace.putMetric('success', success ? 1 : 0);
      await trace.putMetric('duration_ms', duration);

      // Stop the trace
      await trace.stop();
      this.activeTraces.delete(traceId);

      const metrics: PerformanceMetrics = {
        duration,
        success,
        customAttributes: attributes,
      };

      // Also log to Analytics for additional insights
      await analyticsService.trackEvent('performance_trace', {
        [ANALYTICS_PARAMS.TRACE_NAME]: traceName,
        [ANALYTICS_PARAMS.DURATION_MS]: duration,
        [ANALYTICS_PARAMS.SUCCESS]: success,
      });

      console.log(`Performance trace stopped: ${traceName} (${duration}ms)`);
      return metrics;
    } catch (error) {
      console.error(`Failed to stop trace ${traceId}:`, error);
      return null;
    }
  }

  /**
   * Measure and track a function execution
   */
  async measureFunction<T>(traceName: string, fn: () => Promise<T>, attributes?: Record<string, string>): Promise<T> {
    const traceId = await this.startTrace(traceName);
    let success = true;
    let result: T;

    try {
      result = await fn();
      return result;
    } catch (error) {
      success = false;
      throw error;
    } finally {
      await this.stopTrace(traceId, success, attributes);
    }
  }

  /**
   * Track app startup performance
   */
  async trackAppStartup(): Promise<string> {
    return await this.startTrace(PERFORMANCE_TRACES.APP_STARTUP);
  }

  /**
   * Track recipe generation performance
   */
  async trackRecipeGeneration(recipeCount: number, mealType: string): Promise<string> {
    const traceId = await this.startTrace(PERFORMANCE_TRACES.RECIPE_GENERATION);

    // Set initial attributes
    const traceData = this.activeTraces.get(traceId);
    if (traceData) {
      await traceData.trace.putAttribute('recipe_count', recipeCount.toString());
      await traceData.trace.putAttribute('meal_type', mealType);
    }

    return traceId;
  }

  /**
   * Track image analysis performance
   */
  async trackImageAnalysis(photoCount: number): Promise<string> {
    const traceId = await this.startTrace(PERFORMANCE_TRACES.IMAGE_ANALYSIS);

    // Set initial attributes
    const traceData = this.activeTraces.get(traceId);
    if (traceData) {
      await traceData.trace.putAttribute('photo_count', photoCount.toString());
    }

    return traceId;
  }

  /**
   * Track data synchronization performance
   */
  async trackDataSync(operation: string): Promise<string> {
    const traceId = await this.startTrace(PERFORMANCE_TRACES.DATA_SYNC);

    // Set initial attributes
    const traceData = this.activeTraces.get(traceId);
    if (traceData) {
      await traceData.trace.putAttribute('operation', operation);
    }

    return traceId;
  }

  /**
   * Track screen loading performance
   */
  async trackScreenLoad(screenName: string): Promise<string> {
    const traceId = await this.startTrace(PERFORMANCE_TRACES.SCREEN_LOAD);

    // Set initial attributes
    const traceData = this.activeTraces.get(traceId);
    if (traceData) {
      await traceData.trace.putAttribute('screen_name', screenName);
    }

    return traceId;
  }

  /**
   * Track API call performance
   */
  async trackApiCall(endpoint: string, method: string = 'POST'): Promise<string> {
    const traceId = await this.startTrace(PERFORMANCE_TRACES.API_CALL);

    // Set initial attributes
    const traceData = this.activeTraces.get(traceId);
    if (traceData) {
      await traceData.trace.putAttribute('endpoint', endpoint);
      await traceData.trace.putAttribute('method', method);
    }

    return traceId;
  }

  /**
   * Add custom metric to an active trace
   */
  async addMetricToTrace(traceId: string, metricName: string, value: number): Promise<void> {
    try {
      const traceData = this.activeTraces.get(traceId);
      if (traceData) {
        await traceData.trace.putMetric(metricName, value);
        console.log(`Metric added to trace ${traceId}: ${metricName} = ${value}`);
      }
    } catch (error) {
      console.error(`Failed to add metric to trace ${traceId}:`, error);
    }
  }

  /**
   * Add custom attribute to an active trace
   */
  async addAttributeToTrace(traceId: string, attributeName: string, value: string): Promise<void> {
    try {
      const traceData = this.activeTraces.get(traceId);
      if (traceData) {
        await traceData.trace.putAttribute(attributeName, value);
        console.log(`Attribute added to trace ${traceId}: ${attributeName} = ${value}`);
      }
    } catch (error) {
      console.error(`Failed to add attribute to trace ${traceId}:`, error);
    }
  }

  /**
   * Get all active trace IDs
   */
  getActiveTraces(): string[] {
    return Array.from(this.activeTraces.keys());
  }

  /**
   * Stop all active traces (useful for cleanup)
   */
  async stopAllTraces(): Promise<void> {
    const traceIds = this.getActiveTraces();

    for (const traceId of traceIds) {
      await this.stopTrace(traceId, false, { reason: 'cleanup' });
    }

    console.log(`Stopped ${traceIds.length} active traces during cleanup`);
  }

  /**
   * Check if Performance monitoring is initialized
   */
  isPerformanceInitialized(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const performanceService = new PerformanceService();
export default performanceService;
